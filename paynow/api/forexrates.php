<?php

function isJSON($string){
   return is_string($string) && is_array(json_decode($string, true)) ? true : false;
}

$username ="t24admin1";
$password = "#pass1234";
// Create a curl handle to an existing location
$ch = curl_init('https://mobilebanking.stewardbank.co.zw/stbnkcurrencyconverterapi/stewardBankCurrencyConverterAPI');

// Execute
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");
$result = curl_exec($ch);

// Check if any error occurred
if(curl_errno($ch))
{
    echo 'Curl error: ' . curl_error($ch);
}
else{
	
	echo $result;
	if(!isJSON($result)){
		echo 'not json';
		
	}else{
		//echo 'it is json';
		
		$decoded = json_decode($result,true);

		$currencies = $decoded;
		$dateCurrent=$currencies['currencies'][0]['datetime'];

		$time = strtotime($dateCurrent);
		$newformat = date('j F  Y',$time);
		
		?>
		
		
		<link rel="stylesheet" href="style.css">
<div>
    <h1> Foreign Exchange Indications Rates</h1>
    <h1> Date : <?php echo $newformat ?></h1>
    
    <table style="width:70%">
  <tr>
    <th>CURRENCY CODE</th>
    <th>CURRENCY NAME</th> 
    <th>BUYING RATE CASH</th>
    <th>BUYING RATE TT</th>
    <th>SELLING RATE</th>
  </tr>
  <?php 
  
  foreach($currencies['currencies'] as $cur){
	
	?>
  <tr>
    <td><?php echo $cur['currency'] ?></td>
    <td><?php echo $cur['ccyname'] ?></td>
    <td><?php echo $cur['buyrate'] ?></td>
    <td><?php echo $cur['buyrate'] ?></td>
    <td><?php echo $cur['sellrate'] ?></td>
  </tr>
 
  <?php
	}
	
?>
</table>
</div> 
<?php 
	}
}

curl_close($ch); 

?>

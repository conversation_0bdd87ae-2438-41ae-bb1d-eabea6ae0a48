<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


    <changeSet author="mchabanga" id="7ef074da-a9df-1134e8-98d0-529269fb1459">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="request_payment"/>
            </not>
        </preConditions>
        <createTable tableName="request_payment">
            <column name="id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="result_url" type="VARCHAR(250)"/>
            <column name="return_url" type="VARCHAR(250)"/>
            <column name="poll_url" type="VARCHAR(250)">
            </column>
            <column name="browse_url" type="VARCHAR(250)">
            </column>
            <column name="amount" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="provider_status" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="service_status" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="additional_information" type="VARCHAR(600)">
                <constraints nullable="false"/>
            </column>
            <column name="order_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>

            <column name="success" type="BOOLEAN"/>

            <column name="created" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>

            <column name="deleted" type="BOOLEAN"/>

            <column name="updated" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>

        </createTable>

        <addPrimaryKey
                columnNames="id"
                constraintName="request_payment_pkey"
                tableName="request_payment"
        />
        <addAutoIncrement
                columnDataType="int"
                columnName="id"
                incrementBy="1"
                tableName="request_payment"/>

    </changeSet>

    <changeSet author="mchabanga" id="7ef074da-a9df-11e8-98d0-529526t9fb1459">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="successful_payment"/>
            </not>
        </preConditions>
        <createTable tableName="successful_payment">
            <column name="id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="result_url" type="VARCHAR(250)"/>
            <column name="return_url" type="VARCHAR(250)"/>
            <column name="poll_url" type="VARCHAR(250)">
            </column>
            <column name="browse_url" type="VARCHAR(250)">
            </column>
            <column name="amount" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="provider_status" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="service_status" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="additional_information" type="VARCHAR(600)">
                <constraints nullable="false"/>
            </column>
            <column name="order_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>

            <column name="success" type="BOOLEAN"/>

            <column name="created" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>

            <column name="deleted" type="BOOLEAN"/>

            <column name="updated" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>

        </createTable>

        <addPrimaryKey
                columnNames="id"
                constraintName="successful_payment_pkey"
                tableName="successful_payment"
        />
        <addAutoIncrement
                columnDataType="int"
                columnName="id"
                incrementBy="1"
                tableName="successful_payment"/>

    </changeSet>


    <changeSet author="mchabanga" id="7ef074da-a9df-11e8-985a6d0-52952649fb1459">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="unsuccessful_payment"/>
            </not>
        </preConditions>
        <createTable tableName="unsuccessful_payment">
            <column name="id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="result_url" type="VARCHAR(250)"/>
            <column name="return_url" type="VARCHAR(250)"/>
            <column name="poll_url" type="VARCHAR(250)">
            </column>
            <column name="browse_url" type="VARCHAR(250)">
            </column>
            <column name="amount" type="DOUBLE">
                <constraints nullable="false"/>
            </column>
            <column name="provider_status" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="service_status" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="additional_information" type="VARCHAR(600)">
                <constraints nullable="false"/>
            </column>
            <column name="order_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>

            <column name="success" type="BOOLEAN"/>

            <column name="created" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>

            <column name="deleted" type="BOOLEAN"/>

            <column name="updated" type="TIMESTAMP(6)">
                <constraints nullable="false"/>
            </column>

        </createTable>

        <addPrimaryKey
                columnNames="id"
                constraintName="unsuccessful_payment_pkey"
                tableName="unsuccessful_payment"
        />
        <addAutoIncrement
                columnDataType="int"
                columnName="id"
                incrementBy="1"
                tableName="unsuccessful_payment"/>


    </changeSet>


</databaseChangeLog>
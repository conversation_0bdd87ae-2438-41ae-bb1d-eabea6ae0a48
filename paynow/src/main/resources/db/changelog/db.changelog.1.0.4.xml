<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


    <changeSet author="mchabanga" id="7ef23a074da-a9df-113234e8-98d0-529269fb1459">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="retails_merchant_details"/>
            </not>
        </preConditions>
        <createTable tableName="retails_merchant_details">
            <column name="id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="merchant_id" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="merchant_integration_key" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="retail_id" type="VARCHAR(250)">
                <constraints nullable="false" unique="true"/>
            </column>

            <column name="active" type="BOOLEAN"/>


        </createTable>

        <addPrimaryKey
                columnNames="id"
                constraintName="retails_merchant_details_pkey"
                tableName="retails_merchant_details"
        />
        <addAutoIncrement
                columnDataType="int"
                columnName="id"
                incrementBy="1"
                tableName="retails_merchant_details"/>

    </changeSet>




</databaseChangeLog>
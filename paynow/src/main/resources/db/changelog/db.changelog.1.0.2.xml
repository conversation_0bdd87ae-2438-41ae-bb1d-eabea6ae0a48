<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">


    <changeSet author="mchabanga" id="d9a015349e-ea34370-11e8-9f32-f28901f1b9fd1">
    <preConditions onFail="MARK_RAN">
        <not>
            <columnExists columnName="retailer_id" tableName="request_payment"/>
        </not>
    </preConditions>

        <addColumn tableName="request_payment">
            <column name="retailer_id" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

    </changeSet>


    <changeSet author="mchabanga" id="d9a015459e-ea70-143418e8-9f32-f2801f1b9dfd1">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists columnName="retailer_id" tableName="successful_payment"/>
            </not>
        </preConditions>

        <addColumn tableName="successful_payment">
            <column name="retailer_id" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

    </changeSet>


    <changeSet author="mchabanga" id="d9a015945e-e343a70-161e8-9f32-f28f01f1b9fd1">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists columnName="retailer_id" tableName="unsuccessful_payment"/>
            </not>
        </preConditions>

        <addColumn tableName="unsuccessful_payment">
            <column name="retailer_id" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
        </addColumn>

    </changeSet>

</databaseChangeLog>


spring:
  application:
    name: paynow

  profiles:
    active: dev
  cloud:
    config:
      uri: http://localhost:8888/
  main:
    allow-bean-definition-overriding: true
  zipkin:
    base-url: http://localhost:9411/zipkin/
logging:
  level:
    org:
      zalando:
        logbook: TRACE
logbook:
    format.style: json
    include: /api/**
    write:
       category: http.wire-log
       level: INFO
       chunk-size: 1000
extelij:
  merchantId: 675651

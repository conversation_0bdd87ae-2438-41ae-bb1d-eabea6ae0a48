<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="Payment">


    <select id="getAllPayments" resultMap="resultPayment">
        SELECT * FROM payment;
    </select>


    <resultMap id="resultPayment" type="com.extelij.model.Payment">
        <result property="id" column="id"/>
        <result property="amount" column="amount"/>
        <result property="purpose" column="purpose"/>
    </resultMap>

</mapper>
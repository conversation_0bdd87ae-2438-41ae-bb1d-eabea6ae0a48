<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SuccessfulPayment">

    <select id="getPaymentByOrderId" resultMap="resultPayment" parameterType="java.util.Map">
        SELECT * FROM successful_payment  WHERE order_id = #{orderId};
    </select>

    <select id="getPaymentByOrderIdAndStatus" resultMap="resultPayment" parameterType="java.util.Map">
        SELECT * FROM successful_payment  WHERE order_id = #{orderId} AND provider_status = #{status};
    </select>

    <select id="getPaymentByReference" resultMap="resultPayment" parameterType="java.util.Map">
        SELECT * FROM successful_payment  WHERE reference = #{reference};
    </select>

    <select id="getPaymentById" resultMap="resultPayment" parameterType="java.util.Map">
        SELECT * FROM successful_payment  WHERE id = #{id};
    </select>

    <resultMap id="resultPayment" type="com.dephton.core.payment.dao.PaymentDao">
        <result property="id" column="id"/>
        <result property="reference" column="reference"/>
        <result property="resultUrl" column="result_url"/>
        <result property="returnUrl" column="return_url"/>
        <result property="pollUrl" column="poll_url"/>
        <result property="browseUrl" column="browse_url"/>
        <result property="amount" column="amount"/>
        <result property="paynowStatus" column="provider_status"/>
        <result property="serviceStatus" column="service_status"/>
        <result property="additionalInformation" column="additional_information"/>
        <result property="orderId" column="order_id"/>
        <result property="isSuccess" column="success"/>
        <result property="createdAt" column="created"/>
        <result property="isDeleted" column="deleted"/>
        <result property="updatedAt" column="updated"/>
        <result property="paymentProvider" column="payment_provider"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="retailerId" column="retailer_id"/>
        <result property="transactionId" column="transaction_id"/>

    </resultMap>




    <insert id="insertPaymentRequest" parameterType="com.dephton.core.payment.dao.PaymentDao"
            useGeneratedKeys="true" keyProperty="id" keyColumn="ID_">

        insert into successful_payment
        (
        id,
        reference,
        result_url,
        return_url,
        poll_url,
        browse_url,
        amount,
        provider_status,
        service_status,
        additional_information,
        order_id,
        success,
        created,
        deleted,
        updated,
        payment_provider,
        merchant_id,
        retailer_id,
        transaction_id
        )
        values
        (#{id, jdbcType=INTEGER},
        #{reference, jdbcType=VARCHAR},
        #{resultUrl, jdbcType=VARCHAR},
        #{returnUrl, jdbcType=VARCHAR},
        #{pollUrl, jdbcType=VARCHAR},
        #{browseUrl, jdbcType=VARCHAR},
        #{amount, jdbcType=VARCHAR},
        #{paynowStatus, jdbcType=VARCHAR},
        #{serviceStatus, jdbcType=VARCHAR},
        #{additionalInformation, jdbcType=VARCHAR},
        #{orderId, jdbcType=VARCHAR},
        #{isSuccess, jdbcType=BOOLEAN},
        #{createdAt, jdbcType=VARCHAR},
        #{isDeleted, jdbcType=BOOLEAN},
        #{updatedAt, jdbcType=VARCHAR},
        #{paymentProvider, jdbcType=VARCHAR},
        #{merchantId, jdbcType=VARCHAR},
        #{retailerId, jdbcType=VARCHAR},
        #{transactionId, jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateStatusesAndSuccess" parameterType="com.dephton.core.payment.dao.PaymentDao">
        update successful_payment
        set provider_status = #{paynowStatus, jdbcType=VARCHAR},
        service_status = #{serviceStatus, jdbcType=VARCHAR},
        success = #{isSuccess, jdbcType=VARCHAR},
        updated= #{updatedAt, jdbcType=VARCHAR} where order_id = #{orderId, jdbcType=VARCHAR}

    </update>

</mapper>
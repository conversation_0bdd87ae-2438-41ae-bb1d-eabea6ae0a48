<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="RetailMerchantDetails">

    <select id="getByRetailId" resultMap="result" parameterType="java.util.Map">
        SELECT * FROM retails_merchant_details  WHERE retail_id = #{retailId};
    </select>

    <select id="getById" resultMap="result" parameterType="java.util.Map">
        SELECT * FROM retails_merchant_details  WHERE id = #{id};
    </select>

    <select id="getByRetailIdAndActive" resultMap="result" parameterType="java.util.Map">
        SELECT * FROM retails_merchant_details  WHERE retail_id = #{retailId} AND active =  #{active};
    </select>


    <resultMap id="result" type="com.extelij.dao.RetailMerchantDetails">
        <result property="id" column="id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="integrationKey" column="merchant_integration_key"/>
        <result property="retailId" column="retail_id"/>
        <result property="active" column="active"/>
    </resultMap>


    <insert id="insert" parameterType="com.extelij.dao.RetailMerchantDetails"
            useGeneratedKeys="true" keyProperty="id" keyColumn="ID_">

        insert into retails_merchant_details
        (
        id,
        merchant_id,
        merchant_integration_key,
        retail_id,
        active
        )
        values
        (#{id, jdbcType=INTEGER},
        #{merchantId, jdbcType=VARCHAR},
        #{integrationKey, jdbcType=VARCHAR},
        #{retailId, jdbcType=VARCHAR},
        #{active, jdbcType=BOOLEAN}
        )
    </insert>

    <update id="update" parameterType="com.extelij.dao.RetailMerchantDetails">
        update retails_merchant_details
        set merchant_id = #{merchantId, jdbcType=VARCHAR},
        merchant_integration_key = #{integrationKey, jdbcType=VARCHAR},
        retail_id = #{retailId, jdbcType=VARCHAR},
        active= #{active, jdbcType=VARCHAR} where id = #{id, jdbcType=VARCHAR}

    </update>

</mapper>
<?xml version="1.0"?>
<configuration>
    <appender name="FILE_DEPLOYMENT" class="ch.qos.logback.core.FileAppender">
        <file>/srv/extelij/scan2pay/logs/paynow-service-logfile.log</file>
        <append>true</append>
        <encoder>
            <pattern>%-4relative [%thread] %-5level %logger{35} - %msg %n</pattern>
        </encoder>
    </appender>
    <appender name="FILE_LOCAL" class="ch.qos.logback.core.FileAppender">
        <file>/var/log/extelij/scan2pay/paynow-service-logfile.log</file>
        <append>true</append>
        <encoder>
            <pattern>%-4relative [%thread] %-5level %logger{35} - %msg %n</pattern>
        </encoder>
    </appender>
    <appender name="ANALYTICS-FILE" class="ch.qos.logback.core.FileAppender">
        <file>analytics.log</file>
        <append>true</append>
        <encoder>
            <pattern>%-4relative [%thread] %-5level %logger{35} - %msg %n</pattern>
        </encoder>
    </appender>
    <appender name="CONSOLE_HUMAN" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%-4relative [%thread] %-5level %logger{35} - %msg %n</pattern>
        </encoder>
    </appender>

    <appender name="CONSOLE_HUMAN_ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CONSOLE_HUMAN"/>
    </appender>
    <!-- additivity=false ensures analytics data only goes to the analytics log -->
    <logger name="analytics" level="DEBUG" additivity="false">
        <appender-ref ref="ANALYTICS-FILE"/>
    </logger>
    <root level="INFO">
        <appender-ref ref="FILE_LOCAL"/>
        <appender-ref ref="CONSOLE_HUMAN_ASYNC"/>
    </root>
</configuration>
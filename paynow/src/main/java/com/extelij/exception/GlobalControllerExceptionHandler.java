package com.extelij.exception;

import com.dephton.core.payment.dto.ClientMakePaymentResponseDto;
import com.dephton.core.payment.exception.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalControllerExceptionHandler {

	private static final Logger LOGGER = LoggerFactory.getLogger(GlobalControllerExceptionHandler.class);

	@ExceptionHandler(value = {NotFoundException.class})
	@ResponseStatus(HttpStatus.OK)
	public @ResponseBody
	ClientMakePaymentResponseDto notFoundException(NotFoundException ex) {

		LOGGER.error("NotFoundException \n" + ex);
		final ClientMakePaymentResponseDto build = ClientMakePaymentResponseDto.builder()
				.messageDescription(ex.getMessage())
				.success(false).build();

		return build;
	}


	@ExceptionHandler(value = {DuplicateException.class})
	@ResponseStatus(HttpStatus.OK)
	public @ResponseBody
	ClientMakePaymentResponseDto getDuplicateException(DuplicateException ex) {

		LOGGER.error("DuplicateException \n" + ex);
		final ClientMakePaymentResponseDto build = ClientMakePaymentResponseDto.builder()
				.messageDescription(ex.getMessage())
				.success(false).build();

		return build;
	}

	@ExceptionHandler(value = {ValidationException.class})
	@ResponseStatus(HttpStatus.OK)
	public @ResponseBody
	ClientMakePaymentResponseDto validationException(ValidationException ex) {

		LOGGER.error("ValidationException \n" + ex);
		final ClientMakePaymentResponseDto build = ClientMakePaymentResponseDto.builder()
				.messageDescription(ex.getMessage())
				.success(false).build();

		return build;
	}


	@ExceptionHandler(value = {OrderExistException.class})
	@ResponseStatus(HttpStatus.OK)
	public @ResponseBody
	ClientMakePaymentResponseDto getDuplicateOrderException(OrderExistException ex) {

		LOGGER.error("DuplicateOrderException \n" + ex);
		final ClientMakePaymentResponseDto build = ClientMakePaymentResponseDto.builder()
				.messageDescription(ex.getMessage())
				.success(false).build();

		return build;
	}

	@ExceptionHandler(value = {RecreateOrderException.class})
	@ResponseStatus(HttpStatus.OK)
	public @ResponseBody
	ClientMakePaymentResponseDto getRecreateException(RecreateOrderException ex) {

		LOGGER.error("RecreateException \n" + ex);

		final ClientMakePaymentResponseDto build = ClientMakePaymentResponseDto.builder()
				.messageDescription(ex.getMessage())
				.success(false).build();

		return build;
	}


	@ExceptionHandler(Exception.class)
	public final ResponseEntity<ClientMakePaymentResponseDto> handleAllExceptions(Exception ex) {

		ex.printStackTrace();
		LOGGER.error("GLOBAL_EXCEPTION \n" + ex);
		final ClientMakePaymentResponseDto build = ClientMakePaymentResponseDto.builder()
				.messageDescription(ex.getMessage())
				.success(false).build();

		return new ResponseEntity<>(build, HttpStatus.OK);
	}
}

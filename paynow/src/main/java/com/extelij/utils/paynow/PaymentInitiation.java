package com.extelij.utils.paynow;



import com.dephton.core.payment.payment.PaymentRequest;
import com.dephton.core.payment.payment.PaynowInitializationResponse;
import com.extelij.dao.RetailMerchantDetails;
import org.apache.commons.httpclient.URIException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Component
public class PaymentInitiation {

	private Logger LOGGER = LoggerFactory.getLogger(PaymentInitiation.class);
	RestTemplate restTemplate;
	Environment  environment;


	public PaymentInitiation(RestTemplate restTemplate,
	                         Environment environment) {

		this.restTemplate = restTemplate;
		this.environment = environment;
	}

	public PaynowInitializationResponse initializePayment(PaymentRequest paymentRequest, RetailMerchantDetails merchantDetails) throws
			UnsupportedEncodingException,
			URIException {
		String additionalInfo = paymentRequest.getAdditionalinfo(); // description from object in app
		MultiValueMap<String, Object> params = new LinkedMultiValueMap();
		String merchantId=merchantDetails.getMerchantId();
		params.add("id", merchantId);// mechantID
		params.add("reference", paymentRequest.getReference());
		params.add("amount",  paymentRequest.getAmount());
		params.add("additionalinfo", additionalInfo);// tell the user wat they buying
		params.add("returnurl", this.environment.getProperty("application.domain.name") + "/return/" +
				paymentRequest.getOrderId());
		String resultUrl=this.environment.getProperty("application.domain.address") +
				"/result/" + paymentRequest.getOrderId();
		params.add("resulturl", resultUrl);
		params.add("status", "Message");
		String integrationKey=merchantDetails.getIntegrationKey();
		String hash = generateTwoWayHash(params, integrationKey);
		params.add("hash", hash);
		String url=this.environment.getProperty("paynow.url.initialise.transaction");
		String restResponse = this.restTemplate.postForObject(url, params, String.class, new Object[0]);
		return generateGateWayResponse(getResponseAsMap
				(restResponse));


	}


	public String generateTwoWayHash(MultiValueMap<String, Object> dataMapping, String key) {

		StringBuffer buffer = new StringBuffer();
		Iterator var4 = dataMapping.entrySet().iterator();

		while (var4.hasNext()) {
			Map.Entry<String, List<Object>> entry = (Map.Entry) var4.next();
			buffer.append(entry.getValue().toString().trim().replace("[", "").replace("]", ""));
		}

		String joinedValues = buffer.toString();
		String concat = joinedValues + key;
		String utfConcat = concat;

		try {
			utfConcat = new String(concat.getBytes("UTF-8"), "ISO-8859-1");
		} catch (UnsupportedEncodingException var12) {
			this.LOGGER.error("Exception {}", var12);
		}

		MessageDigest md = null;

		try {
			md = MessageDigest.getInstance("SHA-512");
			md.update(utfConcat.getBytes());
		} catch (NoSuchAlgorithmException var11) {
			this.LOGGER.error("Exception {} ", var11);
		}

		byte[] byteData = md.digest();
		StringBuffer sb = new StringBuffer();

		for (int i = 0; i < byteData.length; ++i) {
			sb.append(Integer.toString((byteData[i] & 255) + 256, 16).substring(1).toUpperCase());
		}

		String result = sb.toString();
		return result;
	}


	public Map<String, String> getResponseAsMap(String paynowResponseString) {

		return StringUtil.generateMapFromList(StringUtil.splitStringByTag(paynowResponseString, "&"), "=");
	}

	public PaynowInitializationResponse generateGateWayResponse(Map<String, String> map) throws URIException,
			UnsupportedEncodingException {

		String status = URLDecoder.decode(map.get("status"), StandardCharsets.UTF_8.toString());
		String browserUrl = URLDecoder.decode(map.get("browserurl"), StandardCharsets.UTF_8.toString());
		String pollurl = URLDecoder.decode(map.get("pollurl"), StandardCharsets.UTF_8.toString());
		String hash = map.get("hash");
		PaynowInitializationResponse response = new PaynowInitializationResponse(browserUrl, pollurl, status, hash);
		return response;
	}


}

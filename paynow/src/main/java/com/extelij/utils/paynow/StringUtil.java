package com.extelij.utils.paynow;


import org.apache.commons.httpclient.URIException;
import org.springframework.util.StringUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class StringUtil {


	public static String getShortName(String projectName) {

		String shortName = "";
		String[] tokens = projectName.split(" ");
		switch (tokens.length) {
			case 1:
				shortName = projectName.trim();
				break;
			case 2:
				shortName = tokens[0] + "_" + tokens[1];
				break;
			case 3:
				shortName = tokens[0] + "_" + tokens[2];
				break;
			default:
				shortName = tokens[0] + "_" + tokens[2];
				break;
		}
		return shortName;

	}

	public static List<String> splitStringByTag(String line, String tag) {

		String[] tokens = line.split(tag);
		List<String> items = new ArrayList<String>(Arrays.asList(tokens));
		return items;
	}

	public static Map<String, String> generateMapFromList(List<String> items, String tag) {

		Map<String, String> map = new HashMap<String, String>();
		for (String value : items) {
			String[] tokens = value.split(tag);
			map.put(tokens[0], tokens[1]);

		}
		return map;
	}


	public static String getValueFromKey(Map<String, String> map, String key) throws URIException,
			UnsupportedEncodingException {

		String value = null;

		if(map!=null){
			if (!StringUtils.isEmpty(key)) {

				if (map.containsKey(key)) {
					value = URLDecoder.decode(map.get(key), StandardCharsets.UTF_8.toString());

					return value;
				}
			}

		}

		return value;
	}

}

package com.extelij.controller;


import com.dephton.core.payment.dao.PaymentDao;
import com.google.gson.Gson;
import com.extelij.services.RequestPaymentServiceImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Controller
@RequestMapping("/v1/test/api")
public class TestController {

	RequestPaymentServiceImpl requestPaymentService;

	Gson gson = new Gson();

	public TestController(RequestPaymentServiceImpl requestPaymentService) {

		this.requestPaymentService = requestPaymentService;
	}

	@PostMapping("/saveRequestPayment")
	public ResponseEntity<PaymentDao> savePaymentRequest(@RequestBody PaymentDao paymentDao) {


		PaymentDao savedPaymentDao = requestPaymentService.savePayment(paymentDao);


		return new ResponseEntity<PaymentDao>(savedPaymentDao, HttpStatus.CREATED);
	}

	@GetMapping(value = "/{id}")
	public ResponseEntity<PaymentDao> getPaymentRequestById(@PathVariable("id") Long id) {


		PaymentDao savedPaymentDao = requestPaymentService.findById(id);

		if (savedPaymentDao != null) {
			return new ResponseEntity<>(savedPaymentDao, HttpStatus.CREATED);
		} else {
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);

		}
	}

	@GetMapping(value = "/order", params = {"oderId"})
	public ResponseEntity<PaymentDao> getPaymentRequestByOrderId(@RequestParam("oderId") String orderId) {


		PaymentDao savedPaymentDao = requestPaymentService.findByOrderId(orderId);

		if (savedPaymentDao != null) {
			return new ResponseEntity<>(savedPaymentDao, HttpStatus.CREATED);
		} else {
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);

		}
	}

	@GetMapping(value = "/reference", params = {"reference"})
	public ResponseEntity<PaymentDao> getPaymentRequestByReference(@RequestParam("reference") String orderId) {


		PaymentDao savedPaymentDao = requestPaymentService.findByReference(orderId);

		if (savedPaymentDao != null) {
			return new ResponseEntity<>(savedPaymentDao, HttpStatus.CREATED);
		} else {
			return new ResponseEntity<>(HttpStatus.NOT_FOUND);

		}
	}

	@PutMapping(value = "/updateRequestPayment/{oderId}")
	public ResponseEntity<PaymentDao> updatePaymentRequest(@RequestBody PaymentDao paymentDao, @PathVariable("oderId")
			String oderId) {

		paymentDao.setId(Long.parseLong(oderId));
		paymentDao.setOrderId(oderId);
		paymentDao.setUpdatedAt(new Date());
		PaymentDao savedPaymentDao = requestPaymentService.updatePaymentStatusAndSuccess(paymentDao);


		return new ResponseEntity<>(savedPaymentDao, HttpStatus.CREATED);
	}




}

package com.extelij.controller.paynow;


import com.dephton.core.payment.dao.PaymentDao;
import com.dephton.core.payment.dto.ClientMakePaymentResponseDto;
import com.dephton.core.payment.dto.PollResponseDto;
import com.dephton.core.payment.exception.*;
import com.dephton.core.payment.payment.PaymentRequest;
import com.dephton.core.payment.payment.PaynowInitializationResponse;
import com.dephton.model.AdditionalData;
import com.extelij.dao.RetailMerchantDetails;
import com.extelij.services.*;
import com.extelij.utils.paynow.PaymentInitiation;
import com.google.gson.Gson;
import org.apache.commons.httpclient.URIException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.util.*;

import static com.extelij.utils.paynow.StringUtil.getValueFromKey;

@Controller
@RequestMapping("/paynow")
public class PayNowPaymentRequestController {


	private Logger LOGGER = LoggerFactory.getLogger(PayNowPaymentRequestController.class);
	private PaymentInitiation          paymentInitiation;
	private PollService                pollService;
	private SuccessfulPaymentService   successfulPaymentService;
	private UnSuccessfulPaymentService unSuccessfulPaymentService;
	private RequestPaymentService      requestPaymentService;
	private Gson                       gson;
	private Environment                environment;
	private RestTemplate               restTemplate;
	@Value("${extelij.merchantId}")
	private String                     defaultMerchantId;
	private RetailMerchantDetailsService retailMerchantDetailsService;

	public PayNowPaymentRequestController(PaymentInitiation paymentInitiation,
	                                      PollService pollService,
	                                      SuccessfulPaymentService successfulPaymentService,
	                                      UnSuccessfulPaymentService unSuccessfulPaymentService,
	                                      RequestPaymentService requestPaymentService,
	                                      Environment environment,
	                                      RestTemplate restTemplate,
	                                      RetailMerchantDetailsService retailMerchantDetailsService) {

		this.paymentInitiation = paymentInitiation;
		this.pollService = pollService;
		this.successfulPaymentService = successfulPaymentService;
		this.unSuccessfulPaymentService = unSuccessfulPaymentService;
		this.requestPaymentService = requestPaymentService;
		this.gson = new Gson();
		this.environment = environment;
		this.restTemplate = restTemplate;
		this.retailMerchantDetailsService=retailMerchantDetailsService;
	}

	@PostMapping(value = "/makePayment")
	public ResponseEntity<ClientMakePaymentResponseDto> paynowResultUrlResponseString(@RequestBody PaymentRequest
			                                                                                  paymentRequest) throws
			Exception {

		ClientMakePaymentResponseDto responseError = ClientMakePaymentResponseDto.builder()
				.success(false)
				.responseCode(500)
				.messageDescription("An Error occurred try again")
				.build();


		final RetailMerchantDetails merchantDetails = retailMerchantDetailsService.findByRetailByIdAndActive
				(paymentRequest.getRetailerId(), true);

		if(merchantDetails==null)
			throw new InactivePaymentMethod("Paynow is not active, for this retailer","Inactive payment method");

		try {

			if (paymentRequest.getReference() == null) {
				String reference = UUID.randomUUID().toString();
				paymentRequest.setReference(reference + paymentRequest.getOrderId());
			}
			if (paymentRequest.getAdditionalinfo() == null) {
				paymentRequest.setAdditionalinfo("payment for oderId " + paymentRequest.getOrderId());
			}
			if (paymentRequest.getMerchantId() == null) {
				paymentRequest.setMerchantId(defaultMerchantId);
			}


			LOGGER.info("PAYNOW REQUEST BODY={}", gson.toJson(paymentRequest));


			// save request in request_payment table check if there is no new order exist in that table


			PaymentDao fetchPaymentByOrderId = requestPaymentService.findByOrderId(paymentRequest.getOrderId());

			if (fetchPaymentByOrderId != null) {


				if (!fetchPaymentByOrderId.isSuccess()) {
					//poll orderStatus from Paynow


					if (fetchPaymentByOrderId.getPaymentProvider().equals("PAYNOW")) {

						LOGGER.info("PAYNOW PROVIDER ORDER EXIST ={}", gson.toJson(fetchPaymentByOrderId));

						if (fetchPaymentByOrderId.getPollUrl() != null && !StringUtils.isEmpty
								(fetchPaymentByOrderId
										.getPollUrl
												())) {

							String pollUrl = fetchPaymentByOrderId.getPollUrl();

							ResponseEntity<String> restResponse = this.restTemplate.getForEntity(pollUrl, String
									.class);


							if (restResponse != null) {


								Map<String, String> stringStringMap = paymentInitiation.getResponseAsMap
										(restResponse
												.getBody
														());


								String pollStatus = getValueFromKey(stringStringMap, "status");

								if (pollStatus != null && !StringUtils.isEmpty(pollStatus)) {


									LOGGER.info("PAYNOW PROVIDER STATUS POLLED ={}", pollStatus);

									if (pollStatus.equals("Created") || pollStatus.equals("Sent")) {


										if (fetchPaymentByOrderId.getBrowseUrl() != null && !StringUtils.isEmpty
												(fetchPaymentByOrderId.getBrowseUrl())) {

											ClientMakePaymentResponseDto responseDto = ClientMakePaymentResponseDto
													.builder()
													.browserurl(fetchPaymentByOrderId.getBrowseUrl())
													.orderId(paymentRequest.getOrderId())
													.pollurl(fetchPaymentByOrderId.getPollUrl())
													.status("Created")
													.reference(paymentRequest.getReference())
													.success(true)
													.responseCode(200)
													.messageDescription("Success")
													.build();

											return new ResponseEntity<>(responseDto, HttpStatus.OK);
										} else {
											throw new RecreateOrderException("Recreate Order", "Browse Url was " +
													"not found");
										}


									} else if (pollStatus.equals("Cancelled")) {

										throw new RecreateOrderException("Recreate Order", "Order was cancelled " +
												"" + paymentRequest.getOrderId());
									} else if (pollStatus.equals("Refunded")) {

										throw new RecreateOrderException("Recreate Order", "Order was refunded " +
												"" + paymentRequest.getOrderId());
									} else if (pollStatus.equals("Delivered") || pollStatus.equals("Awaiting " +
											"Delivery") || pollStatus.equals("Paid")) {


										throw new RecreateOrderException("Recreate Order", "was paid successful " +
												"and " +
												"customer needs to buy same order with the same items");

									} else {
										throw new RecreateOrderException("Recreate Order", "Order needs " +
												"recreation" +
												"  " +
												"" + paymentRequest.getOrderId());
									}
								} else {
									throw new RecreateOrderException("Recreate Order", "Status not found for " +
											"order" +
											" " +
											"" + paymentRequest.getOrderId());
								}


							} else {
								throw new RecreateOrderException("Recreate Order", "Error from paynow");
							}
						} else {

							throw new RecreateOrderException("Recreate Order", "A new order must be created " +
									"pollUrl does" +
									" " +
									"not exist");

						}
					} else {
						throw new OrderExistException("Exist Order " + paymentRequest.getOrderId() + " Already  " +
								"Exist " +
								"for" +
								" " +
								"provider " + fetchPaymentByOrderId.getPaymentProvider());
					}


				} else {
					throw new RecreateOrderException("Recreate Order", "was paid successful and " +
							"customer needs to buy same order with the same items");
				}
			}

			PaynowInitializationResponse paynowInitializationResponse = paymentInitiation.initializePayment
					(paymentRequest,merchantDetails);


			if (paynowInitializationResponse != null) {

				ClientMakePaymentResponseDto responseDto = ClientMakePaymentResponseDto.builder()
						.browserurl(paynowInitializationResponse.getBrowserurl())
						.orderId(paymentRequest.getOrderId())
						.pollurl(paynowInitializationResponse.getPollurl())
						.status(paynowInitializationResponse.getStatus())
						.reference(paymentRequest.getReference())
						.success(true)
						.responseCode(200)
						.messageDescription("Success")
						.merchantId(paymentRequest.getMerchantId())
						.build();

				PaymentDao paymentDao = new PaymentDao();
				paymentDao.setReference(paymentRequest.getReference());
				String resultUrl = this.environment.getProperty("application.domain.address") +
						"/result/" + paymentRequest.getOrderId();
				paymentDao.setResultUrl(resultUrl);
				paymentDao.setReturnUrl(this.environment.getProperty("application.domain.address") +
						"/return/" +
						paymentRequest.getOrderId());
				paymentDao.setPollUrl(paynowInitializationResponse.getPollurl());
				paymentDao.setBrowseUrl(paynowInitializationResponse.getBrowserurl());
				paymentDao.setAmount(Double.parseDouble(paymentRequest.getAmount()));
				paymentDao.setPaynowStatus("Created");
				paymentDao.setServiceStatus("Created");
				paymentDao.setAdditionalInformation(paymentRequest.getAdditionalinfo());
				paymentDao.setOrderId(paymentRequest.getOrderId());
				paymentDao.setCreatedAt(new Date());
				paymentDao.setUpdatedAt(new Date());
				paymentDao.setPaymentProvider("PAYNOW");
				paymentDao.setMerchantId(paymentRequest.getMerchantId());
				paymentDao.setRetailerId(paymentRequest.getRetailerId());

				if (paymentRequest.getAdditionalData() != null) {

					AdditionalData additionalData = paymentRequest.getAdditionalData();

					final HashMap<String, Object> data = additionalData.getData();

					if (data != null && data.containsKey("transactionId")) {
						paymentDao.setTransactionId(String.valueOf(data.get("transactionId")) );
					} else {


						throw new ValidationException("Transaction Id is required, ", "additional data must have " +
								"transactionId in hashMap");
					}

				} else {


					throw new ValidationException("Transaction Id is required, ", "additional data must have " +
							"transactionId in hashMap");

				}

				LOGGER.info("PAYMENT SAVE BODY={}", gson.toJson(paymentDao));

				PaymentDao savedPayment = requestPaymentService.savePayment(paymentDao);

				if (savedPayment != null) {


				} else {
					LOGGER.info("ERROR SAVING PAYMENT REQUEST={}", gson.toJson(savedPayment));


					responseError.setMessageDescription("Error occurred while saving transaction, please contact " +
							"support");
					responseError.setSuccess(false);
					responseError.setResponseCode(500);


					return new ResponseEntity<>(responseError, HttpStatus.OK);

				}


				return new ResponseEntity<>(responseDto, HttpStatus.OK);

			}

		} catch (UnsupportedEncodingException e) {

			LOGGER.error("UNSUPPORTED_EXCEPTION {}", e);
		} catch (URIException e) {

			LOGGER.error("URI_EXCEPTION {}", e);
		}
		ClientMakePaymentResponseDto responseDto = ClientMakePaymentResponseDto.builder()
				.success(false)
				.responseCode(500)
				.messageDescription("An error occurred, please try again later")
				.build();
		return new ResponseEntity<>(responseDto, HttpStatus.OK);
	}

	@GetMapping(value = "/poll", params = {"orderId"})
	public ResponseEntity<PollResponseDto> pollStatusByOrderId(@RequestParam("orderId") String orderId) {

		PollResponseDto pollResponseDto = pollService.getPollStatusByOrderId(orderId);

		if (pollResponseDto != null) {

			return new ResponseEntity<>(pollResponseDto, HttpStatus.OK);
		}

		PollResponseDto pollResponseDto1 = new PollResponseDto();
		pollResponseDto1.setSuccess(false);
		pollResponseDto1.setResponseCode(500);
		pollResponseDto1.setMessageDescription("Order not found");
		pollResponseDto1.setPaynowStatus("Expired");
		pollResponseDto1.setCreated(false);


		return new ResponseEntity<>(pollResponseDto1, HttpStatus.OK);

	}

	@GetMapping(value = "/unsuccessful/order", params = {"orderId"})
	public ResponseEntity<List<PaymentDao>> getUnsuccessfulPaymentsByOrderId(@RequestParam("orderId") String orderId) {

		List<PaymentDao> paymentDaos = unSuccessfulPaymentService.findAllByOrderId(orderId);


		return new ResponseEntity<>(paymentDaos, HttpStatus.OK);

	}


	@GetMapping(value = "/successful/order", params = {"orderId"})
	public ResponseEntity<List<PaymentDao>> getSuccessfulPaymentsByOrderId(@RequestParam("orderId") String orderId) {

		List<PaymentDao> paymentDaos = successfulPaymentService.findAllByOrderId(orderId);


		return new ResponseEntity<>(paymentDaos, HttpStatus.OK);

	}

	@GetMapping(value = "/reference", params = {"reference"})
	public ResponseEntity<PaymentDao> findByReference(@RequestParam("reference") String reference) {

		PaymentDao paymentDao = requestPaymentService.findByReference(reference);

		if (paymentDao != null) {
			return new ResponseEntity<>(paymentDao, HttpStatus.OK);
		} else {
			throw new NotFoundException("payment reference "+reference+" was not found", "General error");
		}


	}

	//todo view by dates unsuccessful payments and successful payments
}

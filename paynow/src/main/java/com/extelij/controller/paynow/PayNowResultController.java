package com.extelij.controller.paynow;


import com.dephton.core.payment.dao.PaymentDao;
import com.dephton.core.payment.dto.ClientMakePaymentResponseDto;
import com.dephton.core.payment.dto.PaymentResponseDto;
import com.dephton.core.payment.payment.PaynowInformation;
import com.dephton.dto.PaymentStatusUpdateDto;
import com.dephton.enums.TranStatus;
import com.dephton.model.transaction.TransactionDto;
import com.extelij.proxy.product.ProductProxy;
import com.extelij.proxy.transaction.TransactionProxy;
import com.extelij.utils.paynow.StringUtil;
import com.google.gson.Gson;
import com.extelij.services.RequestPaymentService;
import com.extelij.services.SuccessfulPaymentService;
import com.extelij.services.UnSuccessfulPaymentService;
import com.extelij.utils.paynow.PaymentInitiation;
import org.apache.commons.httpclient.URIException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

import static com.extelij.utils.paynow.StringUtil.getValueFromKey;

@Controller
@RequestMapping("/result")
public class PayNowResultController {

	private Logger LOGGER = LoggerFactory.getLogger(PayNowResultController.class);
	private PaymentInitiation          paymentInitiation;
	private Gson                       gson;
	private RestTemplate               restTemplate;
	private RequestPaymentService      requestPaymentService;
	private SuccessfulPaymentService   successfulPaymentService;
	private UnSuccessfulPaymentService unSuccessfulPaymentService;
	private ProductProxy               productProxy;
	private TransactionProxy           transactionProxy;

	private Environment environment;

	@Autowired
	public PayNowResultController(PaymentInitiation paymentInitiation,
	                              Gson gson,
	                              RestTemplate restTemplate,
	                              RequestPaymentService requestPaymentService,
	                              SuccessfulPaymentService successfulPaymentService,
	                              UnSuccessfulPaymentService unSuccessfulPaymentService,
	                              Environment environment,
	                              ProductProxy productProxy,
	                              TransactionProxy transactionProxy
	) {

		this.paymentInitiation = paymentInitiation;
		this.gson = gson;
		this.restTemplate = restTemplate;
		this.requestPaymentService = requestPaymentService;
		this.successfulPaymentService = successfulPaymentService;
		this.unSuccessfulPaymentService = unSuccessfulPaymentService;
		this.environment = environment;
		this.productProxy = productProxy;
		this.transactionProxy = transactionProxy;


	}

	@PostMapping(value = "/{orderId}", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
	public void paynowResultUrlResponseString(@PathVariable("orderId") Long id, @RequestBody String body) {


		try {
			LOGGER.info("RESULT_FROM_PAY_NOW orderId={}, body={}", id, body);

			if (body != null) {


				Map<String, String> payNowResponseMap = paymentInitiation.getResponseAsMap(body);


				if (payNowResponseMap != null) {

					LOGGER.info("PAY_NOW_RESPONSE_MAP {} ", gson.toJson(payNowResponseMap));


					//get poll url for status confirmation

					String pollUrl = getValueFromKey(payNowResponseMap, "pollurl");
					String reference = getValueFromKey(payNowResponseMap, "reference");
					if (pollUrl != null && !StringUtils.isEmpty(pollUrl)) {

						ResponseEntity<String> restResponse = this.restTemplate.getForEntity(pollUrl, String.class);

						if (restResponse != null) {

							LOGGER.info("POLL RESPONSE {}", gson.toJson(restResponse.getBody()));

							Map<String, String> stringStringMap = paymentInitiation.getResponseAsMap(restResponse
									.getBody
											());

							LOGGER.info("POLL RESPONSE MAP {}", gson.toJson(stringStringMap));

							String pollStatus = getValueFromKey(stringStringMap, "status");

							String paynowResponseStatus = getValueFromKey(payNowResponseMap, "status");


							if (pollStatus != null && paynowResponseStatus != null) {

								if (pollStatus.equals(paynowResponseStatus)) {

									LOGGER.info("STATUS MATCHES SUCCESS");


									//these are for successful payment only
									if (pollStatus.equals("Paid") || pollStatus.equals("Awaiting Delivery") ||
											pollStatus.equals("Delivered")) {


										LOGGER.info("PAID SUCCESSFUL,Poll STATUS={} ", pollStatus);


										//calculate hashing for security, to trust that response is from paynow


										String hash = StringUtil.getValueFromKey(stringStringMap, "hash");
										String pollurl = StringUtil.getValueFromKey(stringStringMap, "pollurl");
										String paynowreference = StringUtil.getValueFromKey(stringStringMap,
												"paynowreference");
										String amount1 = StringUtil.getValueFromKey(stringStringMap, "amount");
										MultiValueMap<String, Object> stringStringHashMap = new LinkedMultiValueMap();
										stringStringHashMap.add("reference", reference);
										stringStringHashMap.add("paynowreference", paynowreference);
										stringStringHashMap.add("amount", amount1);
										stringStringHashMap.add("status", pollStatus);
										stringStringHashMap.add("pollurl", pollurl);
										System.out.println(stringStringHashMap.toString());
										String integrationKey = this.environment.getProperty("paynow.integration.key");
										String hashPaynow = this.paymentInitiation.generateTwoWayHash
												(stringStringHashMap, integrationKey);
										this.LOGGER.info("calculated hash = " + hashPaynow);
										this.LOGGER.info("paynow response hash = " + hash);
										if (hash.equals(hashPaynow)) {
											this.LOGGER.info("EQUAL HASHES");
											this.LOGGER.info("PAID SUCCESSFUL,POLL STATUS {} ", pollStatus);


											// update request payment table status to payment and paymentSuccessful
											// to true


											PaymentDao fetchedDao = requestPaymentService.findByOrderId(String.valueOf
													(id));

											if (fetchedDao != null) {

												LOGGER.info("REQUEST_PAYMENT TABLE UPDATE SUCCESSFUL STATUS={} ",
														paynowResponseStatus);

												fetchedDao.setUpdatedAt(new Date());
												fetchedDao.setSuccess(true);
												fetchedDao.setPaynowStatus(paynowResponseStatus);
												fetchedDao.setServiceStatus("PAID");
												requestPaymentService.updatePaymentStatusAndSuccess(fetchedDao);

												//1 first fetch payment from successful_payment by reference
												//2 check if payment is not exist in success_payment
												//3 if not insert


												PaymentDao fetchedSuccessfulDao = successfulPaymentService
														.findByOrderIdAndPaynowStatus
																(String.valueOf(id), paynowResponseStatus);

												if (fetchedSuccessfulDao != null) {

													LOGGER.info("PAYNOW RESPONSE SUCCESSFUL_PAYMENTS EXIST");

												} else {

													successfulPaymentService.savePayment(fetchedDao);

													LOGGER.info("PAYNOW RESPONSE SUCCESSFUL SAVED IN " +
															"SUCCESSFUL_PAYMENT_TABLE");

												}


												try {
													PaymentStatusUpdateDto paymentStatusUpdateDto = new
															PaymentStatusUpdateDto();

													paymentStatusUpdateDto.setTranStatus(TranStatus.PAID);
													paymentStatusUpdateDto.setSuccess(true);
													paymentStatusUpdateDto.setBasketId(id);
													paymentStatusUpdateDto.setPayNowId(fetchedDao.getId() + "");
													paymentStatusUpdateDto.setBasketReference(fetchedDao.getReference
															());
													productProxy.updateBasketStatus(paymentStatusUpdateDto);

													TransactionDto transactionDto=new TransactionDto();
													transactionDto.setStatus(TranStatus.PAID);
													transactionDto.setId(Long.parseLong(fetchedDao.getTransactionId()));
													transactionDto.setTransactionDescription("payment was successful for order " +
															""+fetchedDao.getOrderId());

													transactionProxy.updateStatus(transactionDto);

												} catch (Exception e) {
													this.LOGGER.info("PaymentStatusUpdateDtoEXCEPTION {}", e);
												}
											}


										} else {
											this.LOGGER.info("NOT EQUAL HASHES");
										}


									} else {

										//these status are for error or changes after sometime
										// reference of payment
										//1. copy payment from request_payment table
										//2. save this payment in unsuccessful_payment table

										PaymentDao fetchedDao = requestPaymentService.findByOrderId(String.valueOf
												(id));

										if (fetchedDao != null) {

											fetchedDao.setUpdatedAt(new Date());
											fetchedDao.setSuccess(false);
											fetchedDao.setPaynowStatus(paynowResponseStatus);
											fetchedDao.setServiceStatus("ERROR");
											unSuccessfulPaymentService.savePayment(fetchedDao);

											try{
												TransactionDto transactionDto=new TransactionDto();
												transactionDto.setStatus(TranStatus.ERROR);
												transactionDto.setId(Long.parseLong(fetchedDao.getTransactionId()));
												transactionDto.setTransactionDescription(body);

												transactionProxy.updateStatus(transactionDto);
											}catch (Exception e) {
												this.LOGGER.info("UNSUCCESSFUL PAYNOW RESPONSE {}", e);
											}


										}




									}


								} else {


									LOGGER.error("STATUS DID NOT MATCH FAILED");
								}

							}
						}

					}

				}


			}

		} catch (Exception e) {
			LOGGER.error("PAYNOW_RESPONSE_EXCEPTION {}", e);
		}

	}


	@GetMapping(
			value = {"paynow/poll/reference"},
			params = {"reference"}
	)

	public ResponseEntity<?> updateStatus(@RequestParam("reference") String reference) throws
			UnsupportedEncodingException, URIException {

		PaymentDao paynowInformationOptional = this.requestPaymentService.findByReference(reference);
		if (paynowInformationOptional != null) {
			PaymentDao paynowInformation = paynowInformationOptional;
			if (paynowInformation.getPollUrl() != null && !StringUtils.isEmpty(paynowInformation.getPollUrl())) {
				String pollUrl = paynowInformation.getPollUrl();
				ResponseEntity<String> restResponse = this.restTemplate.getForEntity(pollUrl, String.class, new
						Object[0]);
				if (restResponse != null) {
					this.LOGGER.info("POLL RESPONSE {}", this.gson.toJson(restResponse.getBody()));
					Map<String, String> stringStringMap = this.paymentInitiation.getResponseAsMap(
							restResponse.getBody());
					this.LOGGER.info("POLL RESPONSE MAP {}", this.gson.toJson(stringStringMap));
					String pollStatus = StringUtil.getValueFromKey(stringStringMap, "status");
					if (pollStatus != null) {
						this.LOGGER.info("STATUS MATCHES SUCCESS");
						if (pollStatus.equals("Paid") || pollStatus.equals("Awaiting Delivery") || pollStatus.equals
								("Delivered")) {
							String hash = StringUtil.getValueFromKey(stringStringMap, "hash");
							String pollurl = StringUtil.getValueFromKey(stringStringMap, "pollurl");
							String paynowreference = StringUtil.getValueFromKey(stringStringMap, "paynowreference");
							String amount1 = StringUtil.getValueFromKey(stringStringMap, "amount");
							MultiValueMap<String, Object> stringStringHashMap = new LinkedMultiValueMap();
							stringStringHashMap.add("reference", reference);
							stringStringHashMap.add("paynowreference", paynowreference);
							stringStringHashMap.add("amount", amount1);
							stringStringHashMap.add("status", pollStatus);
							stringStringHashMap.add("pollurl", pollurl);
							System.out.println(stringStringHashMap.toString());
							String integrationKey = this.environment.getProperty("paynow.integration.key");
							String hashPaynow = this.paymentInitiation.generateTwoWayHash(stringStringHashMap,
									integrationKey);
							this.LOGGER.info("calculated hash = " + hashPaynow);
							this.LOGGER.info("paynow response hash = " + hash);
							if (hash.equals(hashPaynow)) {
								this.LOGGER.info("EQUAL HASHES");
								this.LOGGER.info("PAID SUCCESSFUL,POLL STATUS {} ", pollStatus);



								PaymentDao fetchedDao = requestPaymentService.findByOrderId(String.valueOf
										(paynowInformation.getOrderId()));

								if (fetchedDao != null) {

									LOGGER.info("REQUEST_PAYMENT TABLE  UPDATE SUCCESSFUL STATUS={} ",
											pollStatus);

									fetchedDao.setUpdatedAt(new Date());
									fetchedDao.setSuccess(true);
									fetchedDao.setPaynowStatus(pollStatus);
									fetchedDao.setServiceStatus("PAID");
									requestPaymentService.updatePaymentStatusAndSuccess(fetchedDao);

									//1 first fetch payment from successful_payment by reference
									//2 check if payment is not exist in success_payment
									//3 if not insert


									PaymentDao fetchedSuccessfulDao = successfulPaymentService
											.findByOrderIdAndPaynowStatus
													(String.valueOf(paynowInformation.getOrderId()), pollStatus);

									if (fetchedSuccessfulDao != null) {

										LOGGER.info("PAYNOW RESPONSE SUCCESSFUL_PAYMENTS EXIST");

									} else {

										successfulPaymentService.savePayment(fetchedDao);

										LOGGER.info("PAYNOW RESPONSE SUCCESSFUL SAVED IN " +
												"SUCCESSFUL_PAYMENT_TABLE");

									}
									try {
										PaymentStatusUpdateDto paymentStatusUpdateDto = new
												PaymentStatusUpdateDto();

										paymentStatusUpdateDto.setTranStatus(TranStatus.PAID);
										paymentStatusUpdateDto.setSuccess(true);
										paymentStatusUpdateDto.setBasketId(Long.parseLong(paynowInformationOptional
												.getOrderId()));
										paymentStatusUpdateDto.setPayNowId(fetchedDao.getId() + "");
										paymentStatusUpdateDto.setBasketReference(fetchedDao.getReference());
										productProxy.updateBasketStatus(paymentStatusUpdateDto);

										TransactionDto transactionDto=new TransactionDto();
										transactionDto.setStatus(TranStatus.PAID);
										transactionDto.setId(Long.parseLong(paynowInformationOptional.getTransactionId()));
										transactionDto.setTransactionDescription("payment was successful for order " +
												""+paynowInformationOptional.getOrderId());
										transactionDto.setSuccess(true);
										transactionProxy.updateStatus(transactionDto);



									} catch (Exception e) {
										this.LOGGER.info("PaymentStatusUpdateDtoEXCEPTION {}", e);
									}

								}


							} else {
								this.LOGGER.info("NOT EQUAL HASHES");

								TransactionDto transactionDto=new TransactionDto();
								transactionDto.setStatus(TranStatus.UNEQUAL_HASH);
								transactionDto.setId(Long.parseLong(paynowInformationOptional.getTransactionId()));
								transactionDto.setTransactionDescription("payment was not successful for order " +
										""+paynowInformationOptional.getOrderId()+"\n"+stringStringMap);

								transactionProxy.updateStatus(transactionDto);
							}
						}
					}
				}
			}
		}

		PaymentDao lastFetchAndGetUpdate = this.requestPaymentService.findByReference(reference);
		if (lastFetchAndGetUpdate != null) {
			return new ResponseEntity(lastFetchAndGetUpdate, HttpStatus.OK);
		} else {
			return new ResponseEntity(HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@PutMapping(value = "/reference")
	public ResponseEntity<ClientMakePaymentResponseDto> paynowReturnResponse(@RequestBody PaymentResponseDto
			                                                                         paymentResponseDto) throws
			Exception {


		ClientMakePaymentResponseDto responseDto = ClientMakePaymentResponseDto.builder()
				.messageDescription("An error occurred for order Id " + paymentResponseDto.getBasketId())
				.reference(paymentResponseDto.getBasketReference())
				.orderId(paymentResponseDto.getBasketId())
				.browserurl(paymentResponseDto.getBrowseUrl())
				.pollurl(paymentResponseDto.getPollUrl())
				.paymentProvider(paymentResponseDto.getPaymentProvider())
				.success(false)
				.build();

		//find order id which is basketId
		//request has transaction Id and transaction reference used to update transaction status
		//request has basket reference

		PaymentDao paymentDao = requestPaymentService.findByOrderId(paymentResponseDto.getBasketId());

		if (paymentDao != null) {


			if (paymentResponseDto.getPollUrl() != null && !StringUtils.isEmpty(paymentResponseDto.getPollUrl())) {
				String pollUrl = paymentResponseDto.getPollUrl();
				ResponseEntity<String> restResponse = this.restTemplate.getForEntity(pollUrl, String.class, new
						Object[0]);
				if (restResponse != null) {
					this.LOGGER.info("POLL RESPONSE FROM PAY_NOW {}", this.gson.toJson(restResponse.getBody()));
					Map<String, String> stringStringMap = this.paymentInitiation.getResponseAsMap(
							restResponse.getBody());
					this.LOGGER.info("POLL RESPONSE MAP {}", this.gson.toJson(stringStringMap));
					String pollStatus = StringUtil.getValueFromKey(stringStringMap, "status");

					if (pollStatus != null) {

						if (pollStatus.equals("Paid") || pollStatus.equals("Awaiting Delivery") || pollStatus.equals
								("Delivered")) {
							String hash = StringUtil.getValueFromKey(stringStringMap, "hash");
							String pollurl = StringUtil.getValueFromKey(stringStringMap, "pollurl");
							String paynowreference = StringUtil.getValueFromKey(stringStringMap, "paynowreference");
							String amount1 = StringUtil.getValueFromKey(stringStringMap, "amount");
							MultiValueMap<String, Object> stringStringHashMap = new LinkedMultiValueMap();
							stringStringHashMap.add("reference", paymentResponseDto.getBasketReference());
							stringStringHashMap.add("paynowreference", paynowreference);
							stringStringHashMap.add("amount", amount1);
							stringStringHashMap.add("status", pollStatus);
							stringStringHashMap.add("pollurl", pollurl);
							System.out.println(stringStringHashMap.toString());
							String integrationKey = this.environment.getProperty("paynow.integration.key");
							String hashPaynow = this.paymentInitiation.generateTwoWayHash(stringStringHashMap,
									integrationKey);
							this.LOGGER.info("calculated hash = " + hashPaynow);
							this.LOGGER.info("paynow response hash = " + hash);

							if (hash.equals(hashPaynow)) {

								this.LOGGER.info("EQUAL HASHES PASSED");
								this.LOGGER.info("PAID SUCCESSFUL,POLL STATUS {} ", pollStatus);


								LOGGER.info("REQUEST_PAYMENT TABLE  UPDATE SUCCESSFUL STATUS={} ",
										pollStatus);

								paymentDao.setUpdatedAt(new Date());
								paymentDao.setSuccess(true);
								paymentDao.setPaynowStatus(pollStatus);
								paymentDao.setServiceStatus("PAID");
								requestPaymentService.updatePaymentStatusAndSuccess(paymentDao);

								//1 first fetch payment from successful_payment by reference
								//2 check if payment is not exist in success_payment
								//3 if not insert


								PaymentDao fetchedSuccessfulDao = successfulPaymentService
										.findByOrderIdAndPaynowStatus
												(String.valueOf(paymentResponseDto.getBasketId()), pollStatus);

								if (fetchedSuccessfulDao != null) {

									LOGGER.info("PAYNOW RESPONSE SUCCESSFUL_PAYMENTS EXIST");

								} else {

									successfulPaymentService.savePayment(paymentDao);

									LOGGER.info("PAYNOW RESPONSE SUCCESSFUL SAVED IN " +
											"SUCCESSFUL_PAYMENT_TABLE");

								}
								try {
									PaymentStatusUpdateDto paymentStatusUpdateDto = new
											PaymentStatusUpdateDto();
									paymentStatusUpdateDto.setSuccess(true);
									paymentStatusUpdateDto.setTranStatus(TranStatus.PAID);
									paymentStatusUpdateDto.setBasketId(Long.parseLong(paymentDao
											.getOrderId()));
									paymentStatusUpdateDto.setPayNowId(paymentDao.getId() + "");
									paymentStatusUpdateDto.setBasketReference(paymentDao
											.getReference());
									productProxy.updateBasketStatus(paymentStatusUpdateDto);

									TransactionDto transactionDto=new TransactionDto();
									transactionDto.setStatus(TranStatus.PAID);
									transactionDto.setId(Long.parseLong(paymentDao.getTransactionId()));
									transactionDto.setTransactionDescription("payment was successful for order " +
											""+paymentDao.getOrderId());

									transactionProxy.updateStatus(transactionDto);

								} catch (Exception e) {

									this.LOGGER.info("StatusUpdateEXCEPTION  {}", e);

								}
								responseDto.setStatus("SUCCESS");
								responseDto.setSuccess(true);
								responseDto.setMessageDescription("Payment was successful");
								return ResponseEntity.ok(responseDto);

							} else {
								this.LOGGER.info("NOT EQUAL HASHES");
								responseDto.setSuccess(false);
								responseDto.setMessageDescription("Transaction mismatch");
								paymentDao.setPaynowStatus("UNEQUAL_HASH");
								paymentDao.setPaynowStatus(pollStatus);
								paymentDao.setSuccess(false);
								unSuccessfulPaymentService.savePayment(paymentDao);

								TransactionDto transactionDto=new TransactionDto();
								transactionDto.setStatus(TranStatus.UNEQUAL_HASH);
								transactionDto.setId(Long.parseLong(paymentDao.getTransactionId()));
								transactionDto.setTransactionDescription("payment was not successful for order " +
										""+paymentDao.getOrderId()+"\n"+stringStringMap);

								transactionProxy.updateStatus(transactionDto);

								return ResponseEntity.ok(responseDto);
							}
						} else {

							paymentDao.setPaynowStatus(pollStatus);
							paymentDao.setSuccess(false);
							unSuccessfulPaymentService.savePayment(paymentDao);
							responseDto.setSuccess(false);
							responseDto.setMessageDescription("Payment wasn't paid, status " + pollStatus);

							TransactionDto transactionDto=new TransactionDto();
							transactionDto.setStatus(TranStatus.ERROR);
							transactionDto.setId(Long.parseLong(paymentDao.getTransactionId()));
							transactionDto.setTransactionDescription("payment was not successful for order " +
									""+paymentDao.getOrderId()+" status "+pollStatus+"\n"+stringStringMap);

							transactionProxy.updateStatus(transactionDto);
						}
					}
				}
			}
		}
		responseDto.setMessageDescription("Order " + paymentResponseDto.getBasketId() + " was not found");
		return ResponseEntity.ok(responseDto);
	}

}

package com.extelij.controller.paynow;

import com.dephton.core.payment.exception.InactivePaymentMethod;
import com.extelij.dao.RetailMerchantDetails;
import com.extelij.services.RetailMerchantDetailsService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/merchant")
public class RetailMerchantDetailsController {


	private RetailMerchantDetailsService retailMerchantDetailsService;

	public RetailMerchantDetailsController(RetailMerchantDetailsService retailMerchantDetailsService) {

		this.retailMerchantDetailsService = retailMerchantDetailsService;
	}


	@PostMapping(value = "/detail/save")
	public ResponseEntity<?> save(@RequestBody RetailMerchantDetails
			                              retailMerchantDetails) {

		final RetailMerchantDetails merchantDetails = retailMerchantDetailsService.save(retailMerchantDetails);

		return ResponseEntity.status(HttpStatus.CREATED).body(merchantDetails);


	}


	@GetMapping(
			value = {"/active"},
			params = {"active", "retailId"}
	)
	public ResponseEntity<?> getByRetailIdAndActive(@RequestParam("active") boolean active,
	                                                @RequestParam("retailId") String retailId) {


		final RetailMerchantDetails byRetailByIdAndActive = retailMerchantDetailsService.findByRetailByIdAndActive
				(retailId, active);

		if (byRetailByIdAndActive != null) {
			return ResponseEntity.ok(byRetailByIdAndActive);
		} else {
			throw new InactivePaymentMethod("Paynow is not active, for this retailer","Inactive payment method");
		}

	}

	@GetMapping(value = {"/{id}"})
	public ResponseEntity<?> getById(@PathVariable("id") long id) {


		final RetailMerchantDetails byRetailByIdAndActive = retailMerchantDetailsService.findById(id);

		if (byRetailByIdAndActive != null) {
			return ResponseEntity.ok(byRetailByIdAndActive);
		} else {
			return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
		}

	}

	@PutMapping(value = "/detail/update")
	public ResponseEntity<?> update(@RequestBody RetailMerchantDetails
			                                retailMerchantDetails) {

		final RetailMerchantDetails merchantDetails = retailMerchantDetailsService.update(retailMerchantDetails);

		return ResponseEntity.status(HttpStatus.CREATED).body(merchantDetails);


	}
}

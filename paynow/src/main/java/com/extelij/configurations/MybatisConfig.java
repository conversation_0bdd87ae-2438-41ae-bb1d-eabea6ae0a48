package com.extelij.configurations;

import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Properties;

@Configuration
public class MybatisConfig {


	@Value("${spring.datasource.url}")
	private String url;
	@Value("${spring.datasource.username}")
	private String username;
	@Value("${spring.datasource.password}")
	private String password;


	@Bean
	public DataSource dataSource() {

		DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
		dataSource.setUrl(url);
		dataSource.setUsername(username);
		dataSource.setPassword(password);
		return dataSource;
	}

	@Bean
	public SqlSessionFactoryBean sqlSessionFactory(DataSource dataSource) throws IOException, SQLException {


		SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
		//mybatis
		Properties prop = new Properties();
		prop.setProperty("mapUnderscoreToCamelCase", "true");
		prop.setProperty("dialect", "mysql");
		sqlSessionFactoryBean.setConfigurationProperties(prop);
		sqlSessionFactoryBean.setDataSource(dataSource);
		sqlSessionFactoryBean.setTypeAliasesPackage("com.extelij.model");
		PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();


		sqlSessionFactoryBean.setConfigLocation(resolver.getResource("classpath:mapper/masterconfig.xml"));


		return sqlSessionFactoryBean;
	}

}

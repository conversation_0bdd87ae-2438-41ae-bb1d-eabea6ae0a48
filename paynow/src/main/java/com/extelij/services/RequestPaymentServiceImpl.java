package com.extelij.services;

import com.dephton.core.payment.dao.PaymentDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class RequestPaymentServiceImpl implements RequestPaymentService {

	private SqlSessionFactory sqlSessionFactory;

	@Autowired
	public RequestPaymentServiceImpl(SqlSessionFactory sqlSessionFactory) {

		this.sqlSessionFactory = sqlSessionFactory;
	}


	@Override
	public List<PaymentDao> findAllByStatus() {

		return null;
	}

	@Override
	public PaymentDao findByReference(String reference) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("reference", reference);
			PaymentDao paymentDao = sqlSession.selectOne("RequestPayment.getPaymentByReference", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public PaymentDao findById(Long id) {

		SqlSession sqlSession = null;
		try {
			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("id", id);
			PaymentDao paymentDao = sqlSession.selectOne("RequestPayment.getPaymentById", map);
			return paymentDao;
		} catch (Exception e) {

			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}
		} finally {
			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public PaymentDao findByOrderId(String orderId) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("orderId", orderId);
			PaymentDao paymentDao = sqlSession.selectOne("RequestPayment.getPaymentByOrderId", map);

			return paymentDao;
		} catch (Exception e) {
			log.error("FIND PAYMENT EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public PaymentDao savePayment(PaymentDao paymentDao) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			sqlSession.insert("RequestPayment.insertPaymentRequest", paymentDao);
			paymentDao.setId(paymentDao.getId());


			return paymentDao;
		} catch (Exception e) {
			log.error("SAVE PAYMENT EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;


	}

	@Override
	public PaymentDao updatePayment(PaymentDao paymentDao) {

		return null;
	}

	@Override
	public PaymentDao updatePaymentStatusAndSuccess(PaymentDao paymentDao) {
		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			sqlSession.update("RequestPayment.updateStatusesAndSuccess", paymentDao);

			return paymentDao;
		}catch (Exception e) {
			log.error("UPDATE PAYMENT EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}
		return null;
	}
}

package com.extelij.services;

import com.extelij.dao.RetailMerchantDetails;

/**
 * <AUTHOR>
 */
public interface RetailMerchantDetailsService {

	RetailMerchantDetails save(RetailMerchantDetails retailMerchantDetails);

	RetailMerchantDetails update(RetailMerchantDetails retailMerchantDetails);

	RetailMerchantDetails findById(Long id);

	RetailMerchantDetails findByRetailById(String retailId);

	RetailMerchantDetails findByRetailByIdAndActive(String retailId, boolean active);

	void deleteById(Long id);

}

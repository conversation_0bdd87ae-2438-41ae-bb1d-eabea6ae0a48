package com.extelij.services;


import com.dephton.core.payment.dao.PaymentDao;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Service
public class SuccessfulPaymentServiceImpl implements SuccessfulPaymentService {

	private SqlSessionFactory sqlSessionFactory;

	@Autowired
	public SuccessfulPaymentServiceImpl(SqlSessionFactory sqlSessionFactory) {

		this.sqlSessionFactory = sqlSessionFactory;
	}


	@Override
	public List<PaymentDao> findAllByStatus() {

		return null;
	}

	@Override
	public PaymentDao findByReference(String reference) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("reference", reference);
			PaymentDao paymentDao = sqlSession.selectOne("SuccessfulPayment.getPaymentByReference", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public PaymentDao findById(Long id) {

		SqlSession sqlSession = null;
		try {
			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("id", id);
			PaymentDao paymentDao = sqlSession.selectOne("SuccessfulPayment.getPaymentById", map);
			return paymentDao;
		} catch (Exception e) {

			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}
		} finally {
			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public PaymentDao findByOrderId(String orderId) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("orderId", orderId);
			PaymentDao paymentDao = sqlSession.selectOne("SuccessfulPayment.getPaymentByOrderId", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public PaymentDao savePayment(PaymentDao paymentDao) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			sqlSession.insert("SuccessfulPayment.insertPaymentRequest", paymentDao);
			paymentDao.setId(paymentDao.getId());


			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;


	}

	@Override
	public PaymentDao updatePayment(PaymentDao paymentDao) {

		return null;
	}

	@Override
	public List<PaymentDao> findAllByOrderId(String orderId) {
		SqlSession sqlSession = null;
		try {
			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("orderId", orderId);
			return sqlSession.selectList("SuccessfulPayment.getPaymentByOrderId",map);
		} catch (Exception e) {



			if (sqlSession != null) {
				sqlSession.rollback();
				sqlSession.close();
			}
			e.printStackTrace();
			return null;
		}

	}

	@Override
	public PaymentDao updatePaymentStatusAndSuccess(PaymentDao paymentDao) {
		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			sqlSession.update("SuccessfulPayment.updateStatusesAndSuccess", paymentDao);

			return paymentDao;
		}catch (Exception e) {
			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}
		return null;
	}

	@Override
	public PaymentDao findByOrderIdAndPaynowStatus(String orderId, String status) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("orderId", orderId);
			map.put("status", status);
			PaymentDao paymentDao = sqlSession.selectOne("SuccessfulPayment.getPaymentByOrderIdAndStatus", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}
}

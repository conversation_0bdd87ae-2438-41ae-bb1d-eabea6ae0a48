package com.extelij.services;


import com.dephton.core.payment.dao.PaymentDao;

import java.util.List;

public interface SuccessfulPaymentService {

	List<PaymentDao> findAllByStatus();

	PaymentDao findByReference(String reference);

	PaymentDao findById(Long id);

	PaymentDao findByOrderId(String orderId);

	PaymentDao savePayment(PaymentDao paymentDao);

	PaymentDao updatePayment(PaymentDao paymentDao);

	List<PaymentDao> findAllByOrderId(String orderId);

	PaymentDao updatePaymentStatusAndSuccess(PaymentDao paymentDao);

	PaymentDao findByOrderIdAndPaynowStatus(String orderId,String status);
}

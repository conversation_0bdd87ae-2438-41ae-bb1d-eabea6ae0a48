package com.extelij.services;

import com.extelij.dao.RetailMerchantDetails;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RetailMerchantDetailsServiceImpl implements RetailMerchantDetailsService {


	private SqlSessionFactory sqlSessionFactory;

	@Autowired
	public RetailMerchantDetailsServiceImpl(SqlSessionFactory sqlSessionFactory) {

		this.sqlSessionFactory = sqlSessionFactory;
	}


	@Override
	public RetailMerchantDetails save(RetailMerchantDetails retailMerchantDetails) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			sqlSession.insert("RetailMerchantDetails.insert", retailMerchantDetails);
			retailMerchantDetails.setId(retailMerchantDetails.getId());


			return retailMerchantDetails;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("SAVE_MERCHANT_EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public RetailMerchantDetails update(RetailMerchantDetails retailMerchantDetails) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			sqlSession.update("RetailMerchantDetails.update", retailMerchantDetails);
			retailMerchantDetails.setId(retailMerchantDetails.getId());


			return retailMerchantDetails;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("UPDATE_MERCHANT_EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public RetailMerchantDetails findById(Long id) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("id", id);
			RetailMerchantDetails paymentDao = sqlSession.selectOne("RetailMerchantDetails.getById", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("FIND_BY_ID_EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public RetailMerchantDetails findByRetailById(String retailId) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("retailId", retailId);
			RetailMerchantDetails paymentDao = sqlSession.selectOne("RetailMerchantDetails.getByRetailId", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("FIND_BY_RETAIL_ID_EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public RetailMerchantDetails findByRetailByIdAndActive(String retailId, boolean active) {

		SqlSession sqlSession = null;
		try {

			sqlSession = sqlSessionFactory.openSession();
			Map<String, Object> map = new HashMap<>();
			map.put("retailId", retailId);
			map.put("active", active);
			RetailMerchantDetails paymentDao = sqlSession.selectOne("RetailMerchantDetails.getByRetailIdAndActive", map);

			return paymentDao;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("FIND_BY_RETAIL_ID_AND_ACTIVE_EXCEPTION {}",e);
			if (sqlSession != null) {
				sqlSession.rollback();
			}

		} finally {

			if (sqlSession != null) {
				sqlSession.close();
			}
		}

		return null;
	}

	@Override
	public void deleteById(Long id) {

	}
}

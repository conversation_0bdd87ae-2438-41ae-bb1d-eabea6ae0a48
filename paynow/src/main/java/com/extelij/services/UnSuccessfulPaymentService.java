package com.extelij.services;


import com.dephton.core.payment.dao.PaymentDao;

import java.util.List;

public interface UnSuccessfulPaymentService {

	List<PaymentDao> findAllByStatus();

	PaymentDao findByReference(String reference);

	PaymentDao findById(Long id);

	PaymentDao findByOrderId(String orderId);

	PaymentDao savePayment(PaymentDao paymentDao);

	PaymentDao updatePayment(PaymentDao paymentDao);


	PaymentDao updatePaymentStatusAndSuccess(PaymentDao paymentDao);

	List<PaymentDao> findAllByOrderId(String orderId);

	List<PaymentDao> findALlByReference(String reference);

	PaymentDao findByOrderIdAndPaynowStatus(String orderId,String status);
}

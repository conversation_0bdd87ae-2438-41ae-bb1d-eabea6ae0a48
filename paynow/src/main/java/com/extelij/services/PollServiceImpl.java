package com.extelij.services;

import com.dephton.core.payment.dao.PaymentDao;
import com.dephton.core.payment.dto.PollResponseDto;
import com.google.gson.Gson;

import com.extelij.utils.paynow.PaymentInitiation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

import static com.extelij.utils.paynow.StringUtil.getValueFromKey;

/**
 * <AUTHOR> <PERSON>
 */

@Service
public class PollServiceImpl implements PollService {

	private Logger LOGGER = LoggerFactory.getLogger(PollServiceImpl.class);
	private RestTemplate             restTemplate;
	private RequestPaymentService    requestPaymentService;
	private SuccessfulPaymentService successfulPaymentService;
	private PaymentInitiation        paymentInitiation;

	@Autowired
	public PollServiceImpl(RestTemplate restTemplate,
	                       RequestPaymentService requestPaymentService,
	                       SuccessfulPaymentService successfulPaymentService,
	                       PaymentInitiation paymentInitiation) {

		this.restTemplate = restTemplate;
		this.requestPaymentService = requestPaymentService;
		this.successfulPaymentService = successfulPaymentService;
		this.paymentInitiation = paymentInitiation;
	}

	@Override
	public PollResponseDto getPollStatusByOrderId(String orderId) {

		try {

			PaymentDao fetchedDao = requestPaymentService.findByOrderId(orderId);

			if (fetchedDao != null) {

				String pollUrl = fetchedDao.getPollUrl();
				ResponseEntity<String> restResponse = this.restTemplate.getForEntity(pollUrl, String.class);
				Gson gson = new Gson();
				if (restResponse != null) {

					Map<String, String> stringStringMap = paymentInitiation.getResponseAsMap(restResponse.getBody());

					String paynowResponseStatus = getValueFromKey(stringStringMap, "status");

					if (paynowResponseStatus != null && !StringUtils.isEmpty(paynowResponseStatus)) {

						if (paynowResponseStatus.equals("Paid") || paynowResponseStatus.equals("Awaiting Delivery") ||
								paynowResponseStatus.equals("Delivered")) {


							//check fetchedDao has already set to paid

							if (fetchedDao.isSuccess()) {

								LOGGER.info("PAID BEFORE NO DB UPDATE STATUS={} ", paynowResponseStatus);

							} else {

								//update fetchedDao to paid, status to paynowResponseStatus and success to true

								fetchedDao.setUpdatedAt(new Date());
								fetchedDao.setSuccess(true);
								fetchedDao.setPaynowStatus(paynowResponseStatus);
								fetchedDao.setServiceStatus("PAID");
								requestPaymentService.updatePaymentStatusAndSuccess(fetchedDao);

								LOGGER.info("PAID BEFORE REQUIRE DB UPDATE STATUS={} ", paynowResponseStatus);

								//1 search payment with same order exist
								//2 if not new insert
								//3 if exist update it


								PaymentDao fetchedSuccessfulDao = successfulPaymentService
										.findByOrderIdAndPaynowStatus(orderId,paynowResponseStatus);

								if (fetchedSuccessfulDao != null) {

									LOGGER.info("PAID BEFORE SUCCESSFUL_PAYMENTS EXIST");

								} else {

									successfulPaymentService.savePayment(fetchedDao);

									LOGGER.info("SUCCESSFUL SAVED IN SUCCESSFUL_PAYMENT_TABLE");

								}


							}

							PollResponseDto pollResponseDto = new PollResponseDto();
							pollResponseDto.setBrowseUrl(URLDecoder.decode(fetchedDao.getBrowseUrl(), StandardCharsets
									.UTF_8.toString()));
							pollResponseDto.setSuccess(true);
							pollResponseDto.setResponseCode(200);
							pollResponseDto.setMessageDescription("Success");
							pollResponseDto.setPaynowStatus(paynowResponseStatus);

							return pollResponseDto;
						} else if (paynowResponseStatus.equals("Created")) {
							LOGGER.info("CREATED BEFORE ORDER ID ={} and STATUS={}",orderId,paynowResponseStatus);

							PollResponseDto pollResponseDto = new PollResponseDto();
							pollResponseDto.setBrowseUrl(URLDecoder.decode(fetchedDao.getBrowseUrl(), StandardCharsets
									.UTF_8.toString()));
							pollResponseDto.setSuccess(false);
							pollResponseDto.setResponseCode(201);
							pollResponseDto.setMessageDescription("Created");
							pollResponseDto.setPaynowStatus(paynowResponseStatus);
							pollResponseDto.setCreated(true);
							return pollResponseDto;

						} else {
							LOGGER.info("POLL RESPONSE OTHER ORDER_ID={} and STATUS={}",orderId,paynowResponseStatus);
							PollResponseDto pollResponseDto = new PollResponseDto();
							pollResponseDto.setBrowseUrl(URLDecoder.decode(fetchedDao.getBrowseUrl(), StandardCharsets
									.UTF_8.toString()));
							pollResponseDto.setSuccess(false);
							pollResponseDto.setResponseCode(500);
							pollResponseDto.setMessageDescription(paynowResponseStatus);
							pollResponseDto.setPaynowStatus(paynowResponseStatus);
							pollResponseDto.setCreated(false);
							return pollResponseDto;
						}


					}
				}


			} else {

				return null;
			}


		} catch (Exception e) {

			LOGGER.info("POLL EXCEPTION={} ", e);

		}


		return null;
	}
}

package com.extelij;

import com.extelij.utils.paynow.PaymentInitiation;
import feign.Feign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.security.oauth2.client.EnableOAuth2Sso;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.web.client.RestTemplate;

@EnableOAuth2Sso
@EnableResourceServer
@SpringBootApplication
@ComponentScan("com.extelij")
@EnableFeignClients
@ConditionalOnClass({ Feign.class })
@ConditionalOnProperty(value = "feign.oauth2.enabled", matchIfMissing = true)
public class PaymentApplication implements CommandLineRunner {


	RestTemplate      restTemplate;
	Environment       environment;
	PaymentInitiation paymentInitiation;
	private Logger LOGGER = LoggerFactory.getLogger(PaymentApplication.class);

	public PaymentApplication(RestTemplate restTemplate, Environment environment, PaymentInitiation
			paymentInitiation) {

		this.restTemplate = restTemplate;
		this.environment = environment;
		this.paymentInitiation = paymentInitiation;
	}

	public static void main(String[] args) {

		SpringApplication.run(PaymentApplication.class, args);
	}

	@Override
	public void run(String... args) throws Exception {

//		try {
//
//			Gson gson = new Gson();
//
//			PaymentRequest paymentRequest = new PaymentRequest();
//			paymentRequest.setOrderId("1234");
//			paymentRequest.setAdditionalinfo("Additional Information test");
//			paymentRequest.setAmount("0.5");
//			paymentRequest.setReference("reference");
//			paymentInitiation.initializePayment(paymentRequest);
//
//			PaynowInitializationResponse paynowResponseWrapper = paymentInitiation.initializePayment(paymentRequest);
//
//
//			LOGGER.info("GATE_WAY_RESPONSE {}", gson.toJson(paynowResponseWrapper));
//
//			LOGGER.info("POLL_URL {}", paynowResponseWrapper.getPollurl());
//
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

//		try {
//			String pollUrl = "https://www.paynow.co" +
//					".zw/Interface/CheckPayment/?guid=55d86547-71f5-4384-90c5-12149c8876e1";
//			ResponseEntity<String> restResponse = this.restTemplate.getForEntity(pollUrl, String.class);
//			Gson gson = new Gson();
//			if (restResponse != null) {
//				LOGGER.info("POLL RESPONSE {}", gson.toJson(restResponse.getBody()));
//
//				Map<String, String> stringStringMap = paymentInitiation.getResponseAsMap(restResponse.getBody());
//
//				LOGGER.info("POLL RESPONSE MAP {}", gson.toJson(stringStringMap));
//
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

	}
}

package com.extelij.proxy.transaction;

import com.dephton.model.transaction.TransactionDto;
import com.dephton.model.transaction.TransactionResponseFromPayment;
import com.extelij.proxy.ClientConfiguration;
import org.springframework.cloud.netflix.ribbon.RibbonClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */

@FeignClient(name = "transaction", configuration = ClientConfiguration.class)
@RibbonClient(name = "transaction")
@RequestMapping("/transaction/v1/payment/process")
@Component
public interface TransactionProxy {

	@PostMapping
	ResponseEntity<TransactionResponseFromPayment> processTransaction(@RequestBody TransactionDto transactionDto);

	@PostMapping("/update/status")
	void updateStatus(@RequestBody TransactionDto transactionDto);

}

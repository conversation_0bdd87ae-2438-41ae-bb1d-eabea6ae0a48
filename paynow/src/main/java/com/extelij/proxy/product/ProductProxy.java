package com.extelij.proxy.product;

import com.dephton.dto.PaymentStatusUpdateDto;
import com.extelij.proxy.ClientConfiguration;
import org.springframework.cloud.netflix.ribbon.RibbonClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 */

@FeignClient(name = "product", configuration = ClientConfiguration.class)
@RibbonClient(name = "product")
@RequestMapping("/product")
@Component
public interface ProductProxy {

	@PostMapping("/update-basket-status-from-pay-now-payment-provider")
	void updateBasketStatus(@RequestBody PaymentStatusUpdateDto paymentStatusUpdateDto);

}

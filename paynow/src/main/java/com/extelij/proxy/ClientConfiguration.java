package com.extelij.proxy;

import feign.Logger;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.oauth2.client.DefaultOAuth2ClientContext;
import org.springframework.security.oauth2.client.resource.OAuth2ProtectedResourceDetails;
import org.springframework.security.oauth2.client.token.grant.password.ResourceOwnerPasswordResourceDetails;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class ClientConfiguration {


	@Value("${security.oauth2.client.access-token-uri}")
	private String accessTokenUri;
	@Value("${security.oauth2.client.client-id}")
	private String clientId;
	@Value("${security.oauth2.client.client-secret}")
	private String clientSecret;
	@Value("${security.oauth2.client.scope}")
	private String scope;
	@Value("${oauth2.user.name}")
	private String username;
	@Value("${oauth2.user.pass}")
	private String password;
	@Value("${oauth2.micro.service.token}")
	private String token;


	@Bean
	RequestInterceptor oauth2FeignRequestInterceptor() {

		OAuth2FeignRequestInterceptor oAuth2FeignRequestInterceptor=	new OAuth2FeignRequestInterceptor (new
				DefaultOAuth2ClientContext(), resource
				(),token);

		return oAuth2FeignRequestInterceptor;
	}

	@Bean
	Logger.Level feignLoggerLevel() {
		return Logger.Level.FULL;
	}

	private OAuth2ProtectedResourceDetails resource() {
		ResourceOwnerPasswordResourceDetails resourceDetails = new ResourceOwnerPasswordResourceDetails();
		resourceDetails.setUsername(username);
		resourceDetails.setPassword(password);
		resourceDetails.setAccessTokenUri(accessTokenUri);
		resourceDetails.setClientId(clientId);
		resourceDetails.setClientSecret(clientSecret);
		resourceDetails.setGrantType("password");
		resourceDetails.setScope(Arrays.asList(scope));
		return resourceDetails;
	}


}



package com.extelij.payment;

import com.dephton.core.payment.dao.PaymentDao;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.gson.Gson;
import com.extelij.configurations.BeansConfig;
import com.extelij.configurations.MybatisConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.support.BasicAuthorizationInterceptor;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 */

@ContextConfiguration(classes = {MybatisConfig.class, BeansConfig.class})
@RunWith(MockitoJUnitRunner.class)
public class RequestServiceImplementationTest {

	public RestTemplate template() {

		final RestTemplate restTemplate = new RestTemplate();
		restTemplate.setMessageConverters(Arrays.asList(
				new FormHttpMessageConverter(),
				new StringHttpMessageConverter()
		));
		MappingJackson2HttpMessageConverter jsonHttpMessageConverter = new MappingJackson2HttpMessageConverter();
		jsonHttpMessageConverter.getObjectMapper().configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
		restTemplate.getMessageConverters().add(jsonHttpMessageConverter);
		restTemplate.getInterceptors().add(new BasicAuthorizationInterceptor("client", "secret"));
		return restTemplate;
	}

	private String URL  = "http://localhost:2034/paynow";
	private Gson   gson = new Gson();


	public RequestServiceImplementationTest() {

	}

	@Test
	public void testSavePaymentRequest() {

		System.out.println("hello");
}


//	@Test
//	public void testSavePaymentRequest() {
//
//		PaymentDao paymentDao = new PaymentDao();
//
//
//		paymentDao.setReference(UUID.randomUUID().toString());
//		paymentDao.setResultUrl("http://result_url");
//		paymentDao.setReturnUrl("http://return_url");
//		paymentDao.setPollUrl("https://www.paynow.co.zw/Interface/CheckPayment/?guid=51801473-80c4-41e0-a796-108b9ecf3703");
//		paymentDao.setBrowseUrl("https://www.paynow.co.zw/Payment/ConfirmPayment/1877647");
//		paymentDao.setAmount(1.5);
//		paymentDao.setPaynowStatus("Created");
//		paymentDao.setServiceStatus("Created");
//		paymentDao.setAdditionalInformation("additional");
//		paymentDao.setOrderId(""+6776);
//		paymentDao.setCreatedAt(new Date());
//		paymentDao.setUpdatedAt(new Date());
//		paymentDao.setPaymentProvider("PAYNOW");
//
//		ResponseEntity<PaymentDao> paymentDaoResponseEntity =  template().postForEntity
//				(URL + "/v1/test/api/saveRequestPayment",
//						paymentDao, PaymentDao
//								.class);
//		System.out.println("RESPONSE " + gson.toJson(paymentDaoResponseEntity));
//
//	}


}
